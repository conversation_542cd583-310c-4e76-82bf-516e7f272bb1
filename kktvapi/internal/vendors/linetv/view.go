package linetv

type ManifestFile struct {
	URL  string  `json:"url"`
	Size float64 `json:"size"`
}

type StreamingAsset struct {
	DefaultSubtitle  string            `json:"default_subtitle"`
	SubtitleURL      map[string]string `json:"subtitle_url"`
	ThumbnailURL     string            `json:"thumbnail_url"`
	Dash             ManifestFile      `json:"dash"`
	Hls              ManifestFile      `json:"hls"`
	SupportedQuality []string          `json:"supported_quality"`
}

type PlaybackToken struct {
	Token     string `json:"token"`
	CreatedAt string `json:"created_at"`
	ExpiresAt string `json:"expires_at"`
}

type LicenseHeader struct {
	Key   string   `json:"key"`
	Value string   `json:"value"`
	For   []string `json:"for,omitempty"`
}

type PostEpisodeManifestResponse struct {
	StreamingAssets StreamingAsset    `json:"streaming_assets"`
	LicenseURL      map[string]string `json:"license_url"`
	PlaybackToken   PlaybackToken     `json:"playback_token"`
	LicenseHeaders  []LicenseHeader   `json:"license_headers"`
}
